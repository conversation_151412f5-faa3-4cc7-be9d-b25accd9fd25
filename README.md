# MCP Shell Executor

MCP server for executing local shell commands with security features.

## Development

### Hot Reload Development Mode 🔥

For the best development experience with automatic restart on file changes:

```bash
# Start development server with hot reload
./start-dev.sh

# Start with MCP Inspector
./start-dev.sh --inspector

# Start with TypeScript type checking
./start-dev.sh --type-check

# Start with both inspector and type checking
./start-dev.sh --inspector --type-check
```

### Production Mode

```bash
# Build and start production server
./start.sh

# Start with MCP Inspector for testing
./start.sh --test
```

### Available Scripts

```bash
# Development
npm run dev                 # Hot reload development server
npm run dev:inspector       # Development server + inspector
npm run type-check          # One-time type checking
npm run type-check:watch    # Continuous type checking

# Production
npm run build              # Build TypeScript to dist/
npm run start              # Start production server
```

## Features

- 🔥 **Hot Reload**: Automatic restart on code changes during development
- 🔒 **Security**: Safe command execution with configurable restrictions
- 🧪 **Testing**: Built-in MCP Inspector integration
- ⚡ **Fast**: Uses tsx for lightning-fast TypeScript execution

## TODO

[ ] 数据安全问题？A: 都在本地，不存在数据泄漏的防线