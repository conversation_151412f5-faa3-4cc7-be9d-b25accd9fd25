import { z } from "zod";
import { exec } from "child_process";
import { promisify } from "util";
import * as path from "path";
import { SECURITY_CONFIG, isCommandSafe } from "../../config/security.js";

const execAsync = promisify(exec);

export const executeShellTool = {
  name: "shell",
  config: {
    title: "执行本地脚本",
    description: "执行本地脚本并返回结果",
    inputSchema: {
      command: z.string().describe("The shell command to execute"),
      workingDirectory: z.string().optional().describe("Working directory for the command (optional)"),
      timeout: z.number().optional().describe("Timeout in seconds (max 30, default 10)")
    }
  },
  handler: async (args: any) => {
    const { command, workingDirectory, timeout = 10 } = args;
    try {
      // 安全检查
      const safetyCheck = isCommandSafe(command);
      if (!safetyCheck.safe) {
        return {
          content: [{
            type: "text" as const,
            text: `❌ Command rejected: ${safetyCheck.reason}\n\nCommand: ${command}`
          }],
          isError: true
        };
      }

      // 验证工作目录
      let cwd = process.cwd();
      if (workingDirectory) {
        const resolvedPath = path.resolve(workingDirectory);
        // 确保不能访问系统敏感目录
        const sensitiveDirectories = ['/etc', '/usr/bin', '/usr/sbin', '/bin', '/sbin'];
        if (sensitiveDirectories.some(dir => resolvedPath.startsWith(dir))) {
          return {
            content: [{
              type: "text" as const,
              text: `❌ Access to directory '${workingDirectory}' is not allowed`
            }],
            isError: true
          };
        }
        cwd = resolvedPath;
      }

      // 限制超时时间
      const actualTimeout = Math.min(timeout * 1000, SECURITY_CONFIG.maxExecutionTime);

      // 执行命令
      const startTime = Date.now();
      const { stdout, stderr } = await execAsync(command, {
        cwd,
        timeout: actualTimeout,
        maxBuffer: SECURITY_CONFIG.maxOutputLength
      });

      const executionTime = Date.now() - startTime;
      
      // 构建输出
      let output = `✅ Command executed successfully\n`;
      output += `📁 Working directory: ${cwd}\n`;
      output += `⏱️  Execution time: ${executionTime}ms\n`;
      output += `💻 Command: ${command}\n\n`;

      if (stdout) {
        output += `📤 STDOUT:\n${stdout}\n`;
      }

      if (stderr) {
        output += `⚠️  STDERR:\n${stderr}\n`;
      }

      if (!stdout && !stderr) {
        output += `ℹ️  No output produced\n`;
      }

      return {
        content: [{
          type: "text" as const,
          text: output
        }]
      };

    } catch (error: any) {
      let errorMessage = `❌ Command execution failed\n`;
      errorMessage += `💻 Command: ${command}\n`;
      
      if (error.code === 'ETIMEDOUT') {
        errorMessage += `⏱️  Error: Command timed out after ${timeout} seconds\n`;
      } else if (error.code) {
        errorMessage += `🔢 Exit code: ${error.code}\n`;
      }
      
      if (error.stdout) {
        errorMessage += `📤 STDOUT:\n${error.stdout}\n`;
      }
      
      if (error.stderr) {
        errorMessage += `⚠️  STDERR:\n${error.stderr}\n`;
      }
      
      if (error.message && !error.stdout && !error.stderr) {
        errorMessage += `📝 Error message: ${error.message}\n`;
      }

      return {
        content: [{
          type: "text" as const,
          text: errorMessage
        }],
        isError: true
      };
    }
  }
};
